import Image from "next/image";

export default function LinksPage() {
  const links = [
    {
      title: "Trendyol Mağazası",
      url: "https://www.trendyol.com/magaza/lilys-istanbul-m-633112?sst=0",
      icon: "🛍️"
    },
    {
      title: "Hepsiburada Mağazası", 
      url: "https://www.hepsiburada.com/magaza/lilysistanbul",
      icon: "🛒"
    },
    {
      title: "Instagram",
      url: "https://www.instagram.com/lilysistanbul",
      icon: "📸"
    }
  ];

  return (
    <div className="min-h-screen bg-[#F4C2C2] flex flex-col items-center justify-center p-6">
      <div className="w-full max-w-md mx-auto">
        {/* Logo */}
        <div className="text-center mb-8">
          <Image
            src="/assets/lilys-logo.jpg"
            alt="Lilys Istanbul Logo"
            width={200}
            height={200}
            className="mx-auto rounded-full shadow-lg mb-4"
            priority
          />
          <h1 className="text-4xl font-bold text-black mb-2">Lilys</h1>
          <p className="text-xl text-black/80">İstanbul</p>
        </div>

        {/* Links */}
        <div className="space-y-4">
          {links.map((link, index) => (
            <a
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="block w-full bg-white/90 hover:bg-white text-black font-semibold py-4 px-6 rounded-full text-center transition-all duration-300 hover:scale-105 hover:shadow-lg border-2 border-black/10 hover:border-black/20"
            >
              <span className="text-2xl mr-3">{link.icon}</span>
              {link.title}
            </a>
          ))}
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-black/60 text-sm">
            © 2024 Lilys İstanbul
          </p>
        </div>
      </div>
    </div>
  );
}
