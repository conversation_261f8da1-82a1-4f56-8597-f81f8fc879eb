import Image from "next/image";

export default function LinksPage() {
  const currentYear = new Date().getFullYear();

  const links = [
    {
      title: "Trendyol Mağazası",
      description: "binlerce müşterimizin tercihi",
      url: "https://www.trendyol.com/magaza/lilys-istanbul-m-633112?sst=0",
      logo: "/logos/trendyol.svg",
      bgColor: "bg-orange-500"
    },
    {
      title: "Hepsiburada Mağazası",
      description: "hızlı kargo & güvenli alışveriş",
      url: "https://www.hepsiburada.com/magaza/lilysistanbul",
      logo: "/logos/hepsiburada.svg",
      bgColor: "bg-orange-600"
    },
    {
      title: "Instagram",
      description: "güncel ürünler & müşteri yorumları",
      url: "https://www.instagram.com/lilysistanbul",
      logo: "/logos/instagram.svg",
      bgColor: "bg-gradient-to-r from-purple-500 via-pink-500 to-orange-400"
    },
    {
      title: "WhatsApp",
      description: "anında destek & özel siparişler",
      url: "https://wa.me/905336695459",
      logo: "/logos/whatsapp.svg",
      bgColor: "bg-green-500"
    }
  ];

  return (
    <div className="min-h-screen bg-[#F4C2C2] flex flex-col items-center justify-center p-6">
      <div className="w-full max-w-md mx-auto">
        {/* Logo */}
        <div className="text-center mb-8">
          <Image
            src="/assets/lilys-logo.jpg"
            alt="Lilys Istanbul Logo"
            width={200}
            height={200}
            className="mx-auto rounded-full shadow-lg mb-4"
            priority
          />
          <h1 className="text-4xl font-bold text-black mb-2">Lilys İstanbul</h1>
          <p className="text-sm text-black/60 font-medium">Sağlıklı ve Dayanıklı Özel Plastik Ürünler</p>
        </div>

        {/* Links */}
        <div className="space-y-4">
          {links.map((link, index) => (
            <a
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="block w-full bg-white/90 hover:bg-white text-black font-semibold py-4 px-6 rounded-full text-center transition-all duration-300 hover:scale-105 hover:shadow-lg border-2 border-black/10 hover:border-black/20"
            >
              <div className="flex flex-col items-center">
                <div className="flex items-center mb-1">
                  <div className={`w-8 h-8 rounded-full ${link.bgColor} flex items-center justify-center mr-3 p-1`}>
                    <Image
                      src={link.logo}
                      alt={`${link.title} Logo`}
                      width={24}
                      height={24}
                      className="w-6 h-6 object-contain filter brightness-0 invert"
                    />
                  </div>
                  <span className="font-bold">{link.title}</span>
                </div>
                <span className="text-sm text-black/70 font-normal">({link.description})</span>
              </div>
            </a>
          ))}
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-black/60 text-sm">
            © {currentYear} Lilys İstanbul
          </p>
        </div>
      </div>
    </div>
  );
}
