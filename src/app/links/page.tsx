import Image from "next/image";

export default function LinksPage() {
  const currentYear = new Date().getFullYear();

  const links = [
    {
      title: "Trendyol Mağazası",
      description: "binlerce müşterimizin tercihi",
      url: "https://www.trendyol.com/magaza/lilys-istanbul-m-633112?sst=0",
      logo: "/logos/trendyol.svg",
      gradientFrom: "from-orange-400",
      gradientTo: "to-orange-600",
      logoStyle: "filter brightness-0 invert drop-shadow-sm"
    },
    {
      title: "Hepsiburada Mağazası",
      description: "hızlı kargo & güvenli alışveriş",
      url: "https://www.hepsiburada.com/magaza/lilysistanbul",
      logo: "/logos/hepsiburada.svg",
      gradientFrom: "from-orange-500",
      gradientTo: "to-orange-700",
      logoStyle: "filter brightness-0 invert drop-shadow-sm"
    },
    {
      title: "Instagram",
      description: "günce<PERSON> ürünler & müşteri yorumları",
      url: "https://www.instagram.com/lilysistanbul",
      logo: "/logos/instagram.svg",
      gradientFrom: "from-purple-400",
      gradientTo: "to-pink-600",
      logoStyle: "filter brightness-0 invert drop-shadow-sm"
    },
    {
      title: "WhatsApp",
      description: "anında destek & özel siparişler",
      url: "https://wa.me/905336695459",
      logo: "/logos/whatsapp.svg",
      gradientFrom: "from-green-400",
      gradientTo: "to-green-600",
      logoStyle: "filter brightness-0 invert drop-shadow-sm"
    }
  ];

  return (
    <div className="min-h-screen bg-[#F4C2C2] flex flex-col items-center justify-center p-6">
      <div className="w-full max-w-md mx-auto">
        {/* Logo */}
        <div className="text-center mb-8">
          <Image
            src="/assets/lilys-logo.jpg"
            alt="Lilys Istanbul Logo"
            width={200}
            height={200}
            className="mx-auto rounded-full shadow-lg mb-4"
            priority
          />
          <h1 className="text-4xl font-bold text-black mb-2">Lilys İstanbul</h1>
          <p className="text-sm text-black/60 font-medium">Sağlıklı ve Dayanıklı Özel Plastik Ürünler</p>
        </div>

        {/* Links */}
        <div className="space-y-4">
          {links.map((link, index) => (
            <a
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className={`block w-full bg-gradient-to-r ${link.gradientFrom} ${link.gradientTo} hover:shadow-xl text-white font-semibold py-4 px-6 rounded-2xl text-center transition-all duration-300 hover:scale-[1.02] hover:shadow-lg border border-white/20 hover:border-white/40`}
            >
              <div className="flex items-center justify-start text-left">
                {/* Logo container - daha belirgin */}
                <div className="relative w-12 h-12 mr-4 bg-white/50 rounded-xl flex items-center justify-center backdrop-blur-sm border-2 border-white/40 shadow-lg">
                  <Image
                    src={link.logo}
                    alt={`${link.title} Logo`}
                    width={32}
                    height={32}
                    className={`w-8 h-8 object-contain ${link.logoStyle}`}
                  />
                </div>
                <div className="flex flex-col">
                  <span className="font-bold text-white drop-shadow-sm text-lg">{link.title}</span>
                  <span className="text-sm text-white/90 font-normal">({link.description})</span>
                </div>
              </div>
            </a>
          ))}
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-black/60 text-sm">
            © {currentYear} Lilys İstanbul
          </p>
        </div>
      </div>
    </div>
  );
}
